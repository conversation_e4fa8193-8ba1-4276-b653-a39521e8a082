---
type: "always_apply"
---

# 🧑‍💻 NuGone Tech Stack

This document provides an overview of the technologies, frameworks, and tools used in the NuGone project.

---

## 🏗️ Core Technologies

- **.NET 9** (Primary target framework)
- **C#** (Main programming language)
- **.NET CLI Tool** (Global tool distribution)
- **CQRS** (Command Query Responsibility Segregation for commands)

## 📦 Package & Dependency Management

- **NuGet** (Package management for .NET)
- **MediatR** (Command and query dispatching)
- **Spectre.Console.Cli** (CLI interface framework)
- **Directory.Packages.props** (Central package management support)

## 🧪 Testing

- **xUnit** (Unit and integration testing)
- **coverlet** (Code coverage)

## 🛠️ Build & CI/CD

- **dotnet CLI** (Build, test, and run commands)
- **GitHub Actions** (Continuous Integration/Continuous Deployment)

## 💻 Supported Platforms

- Windows
- macOS
- Linux

## 📁 Supported Project Types

- SDK-style .NET projects (`*.csproj`)
- Multi-project solutions (`*.sln`, `*.slnx`)
- Central package management (`Directory.Packages.props`)

---

For more details, see the [README.md](./README.md) and [PRD.md](./docs/PRD.md).
