using Spectre.Console.Cli;
using System.ComponentModel;
using NuGone.Cli.Shared;

namespace NuGone.Cli.Features.RemoveCommand;

/// <summary>
/// CLI command for removing unused packages.
/// Implements RFC-0001: CLI Architecture And Command Design.
/// </summary>
public class RemoveCommand : BaseCommand<RemoveCommand.Settings>
{
    public class Settings : CommandSettings
    {
        [Description("Path to project or solution")]
        [CommandOption("--project|-p")]
        public string? ProjectPath { get; init; }

        [Description("Exclude packages from removal")]
        [CommandOption("--exclude")]
        public string[]? ExcludePackages { get; init; }

        [Description("Perform a dry run without making actual changes")]
        [CommandOption("--dry-run")]
        public bool DryRun { get; init; }

        [Description("Skip confirmation prompts")]
        [CommandOption("--yes|-y")]
        public bool SkipConfirmation { get; init; }

        [Description("Output format: json or text")]
        [CommandOption("--format|-f")]
        public string Format { get; init; } = "text";

        [Description("Write report to file")]
        [CommandOption("--output|-o")]
        public string? OutputFile { get; init; }

        [Description("Enable verbose output")]
        [CommandOption("--verbose|-v")]
        public bool Verbose { get; init; }

        // TODO: Add validation logic when ValidationResult is properly imported
    }

    protected override int ExecuteCommand(CommandContext context, Settings settings)
    {
        // Validate and resolve project path using base class method
        var projectPath = ValidateAndResolveProjectPath(settings.ProjectPath);

        if (settings.Verbose)
        {
            ConsoleHelpers.WriteVerbose($"Removing packages from: {projectPath}");
            ConsoleHelpers.WriteVerbose($"Dry run: {settings.DryRun}");
            ConsoleHelpers.WriteVerbose($"Output format: {settings.Format}");
            if (settings.ExcludePackages?.Any() == true)
            {
                ConsoleHelpers.WriteVerbose($"Excluded packages: {string.Join(", ", settings.ExcludePackages)}");
            }
        }

        if (settings.DryRun)
        {
            ConsoleHelpers.WriteInfo("Running in dry-run mode - no changes will be made");
        }
        else if (!settings.SkipConfirmation)
        {
            ConsoleHelpers.WriteWarning("This will remove unused packages from your project(s)");
            if (!ConsoleHelpers.Confirm("Do you want to continue?"))
            {
                ConsoleHelpers.WriteInfo("Operation cancelled by user");
                return ExitCodes.Success;
            }
        }

        ConsoleHelpers.WriteInfo("Starting package removal...");

        // TODO: Implement remove command logic using MediatR
        // var command = new RemoveUnusedPackagesCommand(projectPath, settings.DryRun);
        // var result = await mediator.Send(command);

        ConsoleHelpers.WriteSuccess("Package removal completed successfully");
        return ExitCodes.Success;
    }
}
