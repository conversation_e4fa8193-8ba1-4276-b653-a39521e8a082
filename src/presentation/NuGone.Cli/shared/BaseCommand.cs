using Spectre.Console.Cli;
using NuGone.Cli.Shared;

namespace NuGone.Cli.Shared;

/// <summary>
/// Base class for all NuGone CLI commands.
/// Implements RFC-0001: CLI Architecture And Command Design.
/// Provides common functionality and error handling patterns.
/// </summary>
public abstract class BaseCommand<TSettings> : Command<TSettings> 
    where TSettings : CommandSettings
{
    /// <summary>
    /// Executes the command with standardized error handling.
    /// </summary>
    public sealed override int Execute(CommandContext context, TSettings settings)
    {
        try
        {
            return ExecuteCommand(context, settings);
        }
        catch (ArgumentException ex)
        {
            ConsoleHelpers.WriteError($"Invalid argument: {ex.Message}");
            return ExitCodes.InvalidArgument;
        }
        catch (FileNotFoundException ex)
        {
            ConsoleHelpers.WriteError($"File not found: {ex.Message}");
            return ExitCodes.FileNotFound;
        }
        catch (DirectoryNotFoundException ex)
        {
            ConsoleHelpers.WriteError($"Directory not found: {ex.Message}");
            return ExitCodes.DirectoryNotFound;
        }
        catch (UnauthorizedAccessException ex)
        {
            ConsoleHelpers.WriteError($"Access denied: {ex.Message}");
            return ExitCodes.AccessDenied;
        }
        catch (Exception ex)
        {
            ConsoleHelpers.WriteError($"An unexpected error occurred: {ex.Message}");
            if (IsVerboseMode(settings))
            {
                ConsoleHelpers.WriteVerbose($"Stack trace: {ex.StackTrace}");
            }
            return ExitCodes.UnexpectedError;
        }
    }

    /// <summary>
    /// Executes the specific command logic. Override this method in derived classes.
    /// </summary>
    protected abstract int ExecuteCommand(CommandContext context, TSettings settings);

    /// <summary>
    /// Validates the project path and returns the resolved path.
    /// </summary>
    protected string ValidateAndResolveProjectPath(string? projectPath)
    {
        if (string.IsNullOrEmpty(projectPath))
        {
            projectPath = Directory.GetCurrentDirectory();
            ConsoleHelpers.WriteInfo($"No project specified, using current directory: {projectPath}");
        }

        if (!Directory.Exists(projectPath) && !File.Exists(projectPath))
        {
            throw new ArgumentException($"Project path does not exist: {projectPath}");
        }

        return Path.GetFullPath(projectPath);
    }

    /// <summary>
    /// Checks if verbose mode is enabled (if the settings support it).
    /// </summary>
    private static bool IsVerboseMode(TSettings settings)
    {
        // Use reflection to check for a Verbose property
        var verboseProperty = typeof(TSettings).GetProperty("Verbose");
        return verboseProperty?.GetValue(settings) as bool? ?? false;
    }
}

/// <summary>
/// Standard exit codes for NuGone CLI commands.
/// Follows RFC-0001: CLI Architecture And Command Design.
/// </summary>
public static class ExitCodes
{
    public const int Success = 0;
    public const int InvalidArgument = 1;
    public const int FileNotFound = 2;
    public const int DirectoryNotFound = 3;
    public const int AccessDenied = 4;
    public const int UnexpectedError = 99;
}
