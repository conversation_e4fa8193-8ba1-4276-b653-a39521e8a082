<Project>

  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <!-- Core Dependencies -->
    <PackageVersion Include="MediatR" Version="12.4.1" />
    <PackageVersion Include="Spectre.Console.Cli" Version="0.49.0" />
    
    <!-- Testing Dependencies -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="xunit" Version="2.9.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="coverlet.msbuild" Version="6.0.2" />
    
    <!-- Additional Testing Utilities -->
    <PackageVersion Include="FluentAssertions" Version="6.12.1" />
    <PackageVersion Include="Moq" Version="4.20.72" />
  </ItemGroup>

</Project>
