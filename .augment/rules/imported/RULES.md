---
type: "always_apply"
---

# RULES

These rules must be followed for all code generation and review tasks in this project:

1. **SOLID Principles**
   - Ensure code is modular, maintainable, and adheres to SOLID design principles.

2. **Naming & Formatting**
   - Use clear, descriptive names for all identifiers.
   - Apply consistent formatting for maximum readability.

3. **Comments**
   - Add explanatory comments only for non-obvious logic or complex algorithms.
   - Avoid comments for straightforward or self-explanatory code.

4. **Performance & Magic Values**
   - Optimize performance and resource usage without sacrificing clarity.
   - Consider time and space complexity in all solutions.
   - Do not use magic strings or numbers; use constants or enums instead.

5. **Error Handling & Validation**
   - Implement robust error handling and input validation throughout the codebase.

6. **Standard Libraries & Frameworks**
   - Leverage standard libraries and frameworks instead of reinventing common functionality.

7. **Modularity & Coupling**
   - Design code to be modular and loosely coupled to improve testability.

8. **DRY Principle**
   - Avoid duplicating logic; always adhere to DRY (Don't Repeat Yourself) principles.

9. **Security**
   - Follow security best practices (e.g., sanitize inputs) to prevent vulnerabilities.

10. **Defensive Programming**
    - Apply defensive programming techniques: validate all inputs, anticipate incorrect usage, and fail gracefully with informative errors.
